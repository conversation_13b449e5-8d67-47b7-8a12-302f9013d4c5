<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.buyer.home.title') }}</h1>

      <q-space />

      <div class="buttonDiv">
        <q-btn
          class="buttonNeutral"
          v-if="hasRoleConfig"
          icon="o_file_upload"
          :label="$t('entity.action.import')"
          to="/datastreams/upload?type=BUYER"
        />

        <rows-export
          :base-api-url="baseApiUrl"
          class="buttonNeutral"
          :columns="columns"
          :filters="filters"
          :pagination="pagination"
          :visible-columns="visibleColumns"
        />

        <div class="btn-separator" />

        <q-btn class="buttonBrand" v-if="hasRoleAdmin" icon="add" label="new add buyer" @click.stop="createBuyer()" />
        <q-btn class="buttonBrand" v-if="hasRoleWriter" icon="add" :label="$t('afaktoApp.buyer.home.createLabel')" to="/buyers/new" />
      </div>
    </q-toolbar>

    <q-toolbar>
      <columns-filtering v-model="filters" :columns="columns.filter(col => col.filter)" />
      <columns-visibility v-model="visibleColumns" :columns="columns.filter(col => 'hasCover' != col.name)" />
    </q-toolbar>

    <q-table
      v-model:pagination="pagination"
      binary-state-sort
      :columns="columns"
      :loading="loading"
      row-key="id"
      :rows="rows"
      :rows-per-page-options="[0]"
      :visible-columns="visibleColumns"
      @request="onRequest"
      @row-click="(_event, { id }) => router.push(`/buyers/${id}`)"
    >
      <template #header-cell-address_country>
        <q-th class="address_country" />
      </template>
      <template #header-cell-warnings>
        <q-th class="boolean" />
      </template>
      <template #body-cell-createdDate="props">
        <q-td :props="props">
          {{ format(props.row.createdDate) }}
          <q-tooltip>{{ format(props.row.createdDate, 'dd/MM/yyyy HH:mm') }}</q-tooltip>
        </q-td>
      </template>
      <template #body-cell-address_country="props">
        <q-td class="address_country" :props="props">
          <template v-if="props.row.address?.country">
            <em :class="`fi fi-${props.row.address.country.toLowerCase()}`" />
            <q-tooltip>{{ countryNames.of(props.row.address.country.toUpperCase()) }}</q-tooltip>
          </template>
          <template v-else-if="isEnrichable(props.row).enrichable">
            <q-btn class="enrichButton" icon="auto_fix_high" @click.stop="enrichBuyer(props.row)">
              <q-tooltip>{{ t('afaktoApp.buyer.enrichBuyer') }}</q-tooltip>
            </q-btn>
          </template>
          <template v-else>
            <q-btn class="enrichButton" icon="auto_fix_high" disable>
              <q-tooltip>{{ $t(`afaktoApp.buyer.enrichNotAvailable.${isEnrichable(props.row).reason}`) }}</q-tooltip>
            </q-btn>
          </template>
        </q-td>
      </template>
      <template #body-cell-numberType="props">
        <q-td
          class="numberType"
          :props="props"
          v-if="
            props.row.address?.country &&
            tm(`afaktoApp.NumberType.${props.row.address.country.toUpperCase()}.${props.row.numberType}`)?.label
          "
        >
          <q-badge color="blue"
            >{{ t(`afaktoApp.NumberType.${props.row.address.country.toUpperCase()}.${props.row.numberType}.label`) }}
            <q-tooltip>
              {{ $t(`afaktoApp.NumberType.${props.row.numberType}`).replaceAll('_', ' ') }}
            </q-tooltip>
          </q-badge>
        </q-td>
        <q-td class="numberType" :props="props" v-else>
          <q-badge color="blue">{{ $t(`afaktoApp.NumberType.${props.row.numberType}`).replaceAll('_', ' ') }}</q-badge>
        </q-td>
      </template>
      <template #body-cell-number="props">
        <q-td :props="props">
          <template
            v-if="props.row.number && props.row.numberType === 'SIREN' && /^\d+$/.test(props.row.number) && props.row.number.length == 9"
          >
            {{ parseInt(props.row.number).toLocaleString('fr') }}
          </template>
          <template v-else>{{ props.row.number }}</template>
        </q-td>
      </template>
      <template #body-cell-warnings="props">
        <q-td class="boolean">
          <q-icon v-if="props.value" color="warning" name="warning" size="sm">
            <q-tooltip style="white-space: pre-line">
              <ul style="margin: 0; padding-left: 20px">
                <li v-if="props.row.excluded">
                  {{ $t('afaktoApp.buyer.excluded') }}
                  <ul v-if="props.row.exclusionReason">
                    <li>
                      {{ props.row.exclusionReason }}
                    </li>
                  </ul>
                </li>
                <li v-if="props.row.buyerFromFactorUnknown">{{ $t('afaktoApp.buyer.buyerFromFactorUnknown_helper') }}</li>
                <li v-if="hasIncoherentLimit(props.row)">{{ $t('afaktoApp.buyer.incoherentLimit_helper') }}</li>
                <li v-if="hasIncoherentBalance(props.row)">{{ $t('afaktoApp.buyer.incoherentBalance_helper') }}</li>
                <li v-if="hasIncoherentAmount(props.row)">{{ $t('afaktoApp.buyer.incoherentAmount_helper') }}</li>
                <li v-if="hasIncorrectNumber(props.row)">{{ $t('afaktoApp.buyer.incorrectNumber_helper') }}</li>
              </ul>
            </q-tooltip>
          </q-icon>
        </q-td>
      </template>

      <template #body-cell-gauge="props">
        <q-td class="gauge content-center" :props="props" width="200">
          <q-linear-progress
            :value="getProgressValue(props.row)"
            :color="getProgressColor(props.row)"
            size="9px"
            rounded
            style="width: 150px; height: 6px"
          >
            <q-tooltip class="bg-backgroundSecondary rounded" :class="{ 'text-black': !$q.dark.isActive, 'text-white': $q.dark.isActive }">
              <div class="q-pa-xs" style="font-size: 11px">
                <div class="q-mb-xs">
                  <p class="no-margin">{{ $t('afaktoApp.buyer.balance') }} / {{ $t('afaktoApp.buyer.creditLimit') }}</p>
                  <span class="flex justify-around">= {{ n(getProgressPercentage(props.row) * 100, 'percent') }}%</span>
                </div>
              </div>
            </q-tooltip>
          </q-linear-progress>
        </q-td>
      </template>
    </q-table>
    <buyer-creation
      v-model="showBuyerCreation"
      v-if="showBuyerCreation"
      :entity="newBuyer"
      @update:model-value="handleCloseBuyerCreation"
    />
    <buyer-enrich-diff-dialog
      v-model="showEnrichDialog"
      v-if="showEnrichDialog"
      :entity="selectedBuyer"
      :country-filter="selectedBuyerCountryFilter"
      :identifier-type="selectedBuyerIdentifierType"
      @update:model-value="handleCloseBuyerEnrichment"
    />
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import BuyerCreation from 'pages/entities/buyer/BuyerCreation.vue';
import BuyerEnrichDiffDialog from 'pages/entities/buyer/BuyerEnrichDiffDialog.vue';
import { NUMBER_TYPES } from 'src/constants/numberType';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import { filtersToQueryParams } from 'src/util/filtering';
import { findIdType } from 'src/util/findIdType';
import { format } from 'src/util/format';
import useNotifications from 'src/util/useNotifications';

const baseApiUrl = '/api/buyers';
const hasRoleAdmin = useAuthenticationStore().hasRoleAdmin;
const hasRoleConfig = useAuthenticationStore().hasRoleConfig;
const hasRoleWriter = useAuthenticationStore().hasRoleWriter;
const { n, t, tm, te } = useI18n();
const { notifyError } = useNotifications();
const route = useRoute();
const router = useRouter();

const columns = computed(() => [
  {
    align: 'left',
    field: row => row.company?.name,
    filter: {
      field: 'company',
      type: 'enum',
      values: useAuthenticationStore().account.companies.map(company => ({ value: company.id, label: company.name })),
    },
    label: t('afaktoApp.buyer.company'),
    name: 'company.name',
    sortable: true,
  },
  {
    filter: { type: 'boolean' },
    label: t('afaktoApp.invoice.hasCover'),
    hidden: true,
    name: 'hasCover',
  },
  {
    align: 'left',
    field: 'code',
    filter: { type: 'text' },
    label: t('afaktoApp.buyer.code'),
    name: 'code',
    sortable: true,
  },
  {
    align: 'left',
    field: row => row.address?.country,
    filter: { type: 'country' },
    label: t('afaktoApp.address.country'),
    name: 'address_country',
    sortable: true,
  },
  {
    align: 'left',
    field: 'name',
    filter: { type: 'text' },
    label: t('afaktoApp.buyer.name'),
    name: 'name',
    sortable: true,
  },
  {
    field: 'numberType',
    filter: {
      type: 'enum',
      values: NUMBER_TYPES.map(numberType => ({
        label: t(`afaktoApp.NumberType.${numberType}`).replaceAll('_', ' '),
        value: numberType,
      })),
    },
    headerClasses: 'numberType',
    label: t('afaktoApp.buyer.numberType'),
    name: 'numberType',
    sortable: true,
  },
  {
    align: 'left',
    field: 'number',
    filter: { type: 'text' },
    label: t('afaktoApp.buyer.buyerID'),
    name: 'number',
    sortable: true,
  },
  {
    align: 'center',
    field: 'createdDate',
    label: t('global.field.createdDate'),
    name: 'createdDate',
    sortable: true,
  },
  {
    field: row => hasAnyWarning(row),
    filter: false,
    label: t('global.field.warnings'),
    name: 'warnings',
    sortable: true,
  },
  {
    field: row => row.excluded,
    filter: { type: 'boolean' },
    label: t('afaktoApp.buyer.excluded'),
    name: 'excluded',
    sortable: true,
    hidden: true,
  },
  {
    field: 'currency',
    filter: { type: 'currency' },
    label: t('afaktoApp.buyer.currency'),
    name: 'currency',
    sortable: true,
  },
  {
    classes: 'text-positive',
    field: row => row.creditLimit?.amount,
    filter: { type: 'number' },
    format: (value, row) => (value == null ? '' : n(value, 'currency', { currency: row.creditLimit?.currency })),
    label: t('afaktoApp.buyer.creditLimit'),
    name: 'creditLimit_amount',
    sortable: true,
  },
  {
    field: row => hasAnyWarning(row),
    filter: false,
    label: t('global.field.warnings'),
    name: 'warnings',
    sortable: true
  },
  {
    field: row => row.buyerFromFactorUnknown,
    filter: { type: 'boolean' },
    label: t('afaktoApp.buyer.buyerFromFactorUnknown'),
    name: 'buyerFromFactorUnknown',
    sortable: true,
    hidden: true,
  },
  {
    classes: row => (row.buyerFromFactor?.amountApproved < 0 ? 'text-negative' : 'text-positive'),
    field: row => row.buyerFromFactor?.amountApproved,
    filter: { type: 'number' },
    format: (value, row) => (value == null ? '' : n(value, 'currency', { currency: row.buyerFromFactor?.currency })),
    hidden: true,
    label: t('afaktoApp.buyer.buyerFromFactor.amountApproved'),
    name: 'buyerFromFactor_amountApproved',
    sortable: true,
  },
  {
    field: row => row.buyerFromFactor && row.creditLimit?.amount != row.buyerFromFactor?.amountApproved,
    filter: { type: 'boolean' },
    label: t('afaktoApp.buyer.incoherentLimit'),
    name: 'incoherentLimit',
    sortable: true,
    hidden: true,
  },
  {
    classes: row => (row.balance < 0 ? 'text-negative' : 'text-positive'),
    field: row => row.balance,
    filter: { type: 'number' },
    format: (value, row) => (value == null ? '' : n(value, 'currency', { currency: row?.currency })),
    label: t('afaktoApp.buyer.balance'),
    name: 'balance',
    sortable: true,
  },
  {
    field: row => row.creditLimit && row.balance > row.creditLimit.amount,
    filter: { type: 'boolean' },
    label: t('afaktoApp.buyer.incoherentBalance'),
    name: 'incoherentBalance',
    sortable: true,
    hidden: true,
  },
  {
    classes: row => (row.buyerFromFactor?.amountOutstanding < 0 ? 'text-negative' : 'text-positive'),
    field: row => row.buyerFromFactor?.amountOutstanding,
    filter: { type: 'number' },
    format: (value, row) => (value == null ? '' : n(value, 'currency', { currency: row.buyerFromFactor?.currency })),
    hidden: true,
    label: t('afaktoApp.buyer.buyerFromFactor.amountOutstanding'),
    name: 'buyerFromFactor_amountOutstanding',
    sortable: true,
  },
  {
    field: row => row.buyerFromFactor && row.buyerFromFactor.amountOutstanding > row.buyerFromFactor.amountApproved,
    filter: { type: 'boolean' },
    label: t('afaktoApp.buyer.incoherentAmount'),
    name: 'incoherentAmount',
    sortable: true,
    hidden: true,
  },
  {
    classes: row => (row.buyerFromFactor?.amountFunded < 0 ? 'text-negative' : 'text-positive'),
    field: row => row.buyerFromFactor?.amountFunded,
    filter: { type: 'number' },
    format: (value, row) => (value == null ? '' : n(value, 'currency', { currency: row.buyerFromFactor?.currency })),
    label: t('afaktoApp.buyer.buyerFromFactor.amountFunded'),
    name: 'buyerFromFactor_amountFunded',
    sortable: true,
  },
  {
    align: 'left',
    filter: false,
    label: t('afaktoApp.buyer.buyerFromFactor.gauge'),
    name: 'gauge',
  },
]);

const visibleColumns = ref([]);
const filters = ref({});
const store = useAuthenticationStore();
watch(filters, () => onRequest({ pagination: pagination.value }), { deep: true });

const pagination = ref({
  sortBy: route.query.sortBy || 'createdDate',
  descending: route.query.descending !== 'false',
  page: Number.parseInt(route.query.page || 1),
  rowsPerPage: store._account.preferences.ROWS_PER_PAGE || 25,
  rowsNumber: 15,
});

const loading = ref(true);
const rows = ref([]);
const onRequest = async ({ pagination: { page, rowsPerPage, sortBy, descending } }) => {
  loading.value = true;
  const response = await api.get(baseApiUrl, {
    params: {
      page: page - 1,
      size: rowsPerPage === 0 ? pagination.value.rowsNumber : rowsPerPage,
      sort: `${sortBy},${descending ? 'desc' : 'asc'}`,
      ...filtersToQueryParams(filters.value),
    },
  });
  rows.value = response.data;

  await loadEnrichmentCache(rows.value);

  pagination.value = { rowsNumber: response.headers['x-total-count'], page, rowsPerPage, sortBy, descending };
  router.replace({ query: { page, sortBy, descending, rowsPerPage } });

  loading.value = false;
};

const getProgressPercentage = row => {
  if (!row.balance) return 0;
  if (!row.creditLimit?.amount) return 1;

  return row.balance / row.creditLimit.amount;
};

const getProgressValue = row => Math.min(getProgressPercentage(row), 1);

const getProgressColor = row => {
  const percentage = getProgressPercentage(row);

  if (percentage >= 1) return 'negative';
  if (percentage >= 0.75) return 'warning';
  return 'positive';
};

const countryNames = new Intl.DisplayNames([navigator.language], { type: 'region' });

const showBuyerCreation = ref(false);
const newBuyer = ref(null);

// Helper functions for warnings
const hasIncoherentLimit = row => row.buyerFromFactor && row.creditLimit?.amount != row.buyerFromFactor?.amountApproved;
const hasIncoherentBalance = row => row.creditLimit && row.balance > row.creditLimit.amount;
const hasIncoherentAmount = row => row.buyerFromFactor && row.buyerFromFactor.amountOutstanding > row.buyerFromFactor.amountApproved;
const hasAnyWarning = row =>
  row.excluded ||
  row.buyerFromFactorUnknown ||
  hasIncoherentLimit(row) ||
  hasIncoherentBalance(row) ||
  hasIncoherentAmount(row) ||
  hasIncorrectNumber(row);

function hasIncorrectNumber(row) {
  if (!row.address?.country || !row.numberType || !row.number) return false;

  const country = row.address.country.toUpperCase();
  const type = row.numberType;
  const regexPath = `afaktoApp.NumberType.${country}.${type}.regex`;

  if (!te(regexPath)) return false;

  try {
    const pattern = tm(regexPath);
    const regex = new RegExp(pattern);
    return !regex.test(row.number);
  } catch (err) {
    console.warn(`Invalid regex for ${regexPath}:`, err);
    return false;
  }
}

async function createBuyer() {
  newBuyer.value = {
    address: { city: '' },
    contact: { name: '' },
  };
  showBuyerCreation.value = true;
}

const handleCloseBuyerEnrichment = value => {
  if (value) return;
  selectedBuyerIdentifierType.value = [];
  selectedBuyerCountryFilter.value = [];
};

const handleCloseBuyerCreation = value => {
  if (value) return;
  onRequest({ pagination: pagination.value });
};

const enrichableMap = ref({});
const i18n = useI18n();

function isEnrichable(buyer) {
  if (loading.value) return { enrichable: false, reason: 'LOADING' };
  if (buyer.number?.length < 3) return { enrichable: false, reason: 'INCORRECT_NUMBER' };

  if (enrichableMap.value[buyer.id] == null) return { enrichable: true };

  const { identifierType, countryFilter } = enrichableMap.value[buyer.id];
  const hasOptions = Object.keys(countryFilter).length || Object.keys(identifierType).length;
  return hasOptions ? { enrichable: true } : { enrichable: false, reason: 'INCORRECT_NUMBER' };
}

async function loadEnrichmentCache(buyers) {
  enrichableMap.value = {};
  for (const buyer of buyers) {
    if (buyer.address?.country && (buyer?.number || buyer.number !== '0')) continue;

    const { evaluateIdentifier, identifierType, countryFilter } = findIdType(buyer, i18n);
    await evaluateIdentifier(buyer.number);

    enrichableMap.value[buyer.id] = { identifierType: identifierType.value, countryFilter: countryFilter.value };
  }
}

const selectedBuyer = ref(null);
const selectedBuyerIdentifierType = ref([]);
const selectedBuyerCountryFilter = ref([]);
const showEnrichDialog = ref(false);

async function enrichBuyer(buyer) {
  if (enrichableMap.value[buyer.id] == null) {
    selectedBuyer.value = buyer;
    showEnrichDialog.value = true;
    return;
  }

  const { identifierType, countryFilter } = enrichableMap.value[buyer.id];

  const countryKeys = Object.keys(countryFilter);
  if (Object.keys(identifierType).length === 0 && countryKeys.length === 0) {
    notifyError("shouldn't happen");
    return;
  }

  selectedBuyer.value = buyer;
  selectedBuyerIdentifierType.value = identifierType;
  selectedBuyerCountryFilter.value = countryFilter;
  showEnrichDialog.value = true;
}
</script>
